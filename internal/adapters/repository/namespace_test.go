package repository

import (
	"testing"
	"time"

	"ops-api/internal/core/domain"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Auto migrate all tables
	err = db.AutoMigrate(
		&domain.Namespace{},
		&domain.Deployment{},
		&domain.Service{},
		&domain.Ingress{},
		&domain.IngressSpec{},
		&domain.Domain{},
		&domain.Environment{},
		&domain.Cluster{},
		&domain.ServerStatus{},
	)
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}

	return db
}

func createTestData(t *testing.T, db *gorm.DB) uint64 {
	// Create a test cluster
	cluster := &domain.Cluster{
		BaseModel: domain.BaseModel{
			ID:        1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:      "test-cluster",
		Region:    "us-east-1",
		PoolName:  "test-pool",
		Size:      "small",
		NodeCount: 1,
		StatusID:  1,
	}
	if err := db.Create(cluster).Error; err != nil {
		t.Fatalf("Failed to create test cluster: %v", err)
	}

	// Create a test server status
	status := &domain.ServerStatus{
		BaseModel: domain.BaseModel{
			ID:        1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name: domain.ServerStatusActive,
	}
	if err := db.Create(status).Error; err != nil {
		t.Fatalf("Failed to create test status: %v", err)
	}

	// Create a test namespace
	namespace := &domain.Namespace{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:      "test-namespace",
		Slug:      "test-namespace",
		IsActive:  true,
		Type:      domain.NamespaceTypeDraft,
		ClusterID: cluster.ID,
	}
	if err := db.Create(namespace).Error; err != nil {
		t.Fatalf("Failed to create test namespace: %v", err)
	}

	// Create a test deployment
	deployment := &domain.Deployment{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:          "test-deployment",
		Image:         "nginx:latest",
		ContainerPort: 80,
		Replicas:      1,
		NamespaceID:   namespace.ID,
		StatusID:      status.ID,
	}
	if err := db.Create(deployment).Error; err != nil {
		t.Fatalf("Failed to create test deployment: %v", err)
	}

	// Create a test environment for the deployment
	environment := &domain.Environment{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:         "TEST_ENV",
		Value:        "test-value",
		DeploymentID: deployment.ID,
	}
	if err := db.Create(environment).Error; err != nil {
		t.Fatalf("Failed to create test environment: %v", err)
	}

	// Create a test service
	service := &domain.Service{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:         "test-service",
		Port:         "80",
		TargetPort:   "80",
		Type:         "ClusterIP",
		NamespaceID:  namespace.ID,
		DeploymentID: deployment.ID,
		StatusID:     status.ID,
	}
	if err := db.Create(service).Error; err != nil {
		t.Fatalf("Failed to create test service: %v", err)
	}

	// Create a test ingress
	ingress := &domain.Ingress{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:        "test-ingress",
		Class:       "nginx",
		NamespaceID: namespace.ID,
		StatusID:    status.ID,
	}
	if err := db.Create(ingress).Error; err != nil {
		t.Fatalf("Failed to create test ingress: %v", err)
	}

	// Create a test ingress spec
	ingressSpec := &domain.IngressSpec{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Host:      "test.example.com",
		Path:      "/",
		Port:      80,
		ServiceID: service.ID,
		IngressID: ingress.ID,
	}
	if err := db.Create(ingressSpec).Error; err != nil {
		t.Fatalf("Failed to create test ingress spec: %v", err)
	}

	// Create a test domain
	testDomain := &domain.Domain{
		BaseModel: domain.BaseModel{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		Name:        "test.example.com",
		IsDefault:   true,
		IsActive:    true,
		ZoneID:      "zone123",
		AccountID:   "account123",
		AccountName: "test-account",
		NamespaceID: namespace.ID,
		Index:       1,
	}
	if err := db.Create(testDomain).Error; err != nil {
		t.Fatalf("Failed to create test domain: %v", err)
	}

	return namespace.ID
}

func TestNamespaceRepository_Delete_CascadingDeletion(t *testing.T) {
	db := setupTestDB(t)
	repo := NewNamespaceRepository(db)

	// Create test data
	namespaceID := createTestData(t, db)

	// Verify namespace exists before deletion
	var count int64
	db.Model(&domain.Namespace{}).Where("id = ?", namespaceID).Count(&count)
	if count != 1 {
		t.Errorf("Expected 1 namespace, got %d", count)
	}

	// Perform cascading deletion
	err := repo.Delete(namespaceID)
	if err != nil {
		t.Fatalf("Failed to delete namespace: %v", err)
	}

	// Verify namespace is deleted
	db.Model(&domain.Namespace{}).Where("id = ?", namespaceID).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 namespaces after deletion, got %d", count)
	}

	// Verify related entities are also deleted
	db.Model(&domain.Deployment{}).Where("namespace_id = ?", namespaceID).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 deployments after deletion, got %d", count)
	}

	db.Model(&domain.Service{}).Where("namespace_id = ?", namespaceID).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 services after deletion, got %d", count)
	}

	db.Model(&domain.Ingress{}).Where("namespace_id = ?", namespaceID).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 ingresses after deletion, got %d", count)
	}

	db.Model(&domain.Domain{}).Where("namespace_id = ?", namespaceID).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 domains after deletion, got %d", count)
	}

	// Check that all ingress specs and environments are also deleted
	db.Model(&domain.IngressSpec{}).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 ingress specs after deletion, got %d", count)
	}

	db.Model(&domain.Environment{}).Count(&count)
	if count != 0 {
		t.Errorf("Expected 0 environments after deletion, got %d", count)
	}
}
